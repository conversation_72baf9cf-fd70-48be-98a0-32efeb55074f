import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Megaphone, Trophy, Users, Archive, LogIn, LogOut, User } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const Navigation = () => {
  const location = useLocation();
  const { isAuthenticated, profile, logout } = useAuth();

  const navItems = [
    { path: "/", label: "Announcements", icon: Megaphone },
    { path: "/hall-of-fame", label: "Hall of Fame", icon: Trophy },
    { path: "/members", label: "Members", icon: Users },
    { path: "/archive", label: "Archive", icon: Archive },
  ];

  return (
    <nav className="sticky top-0 z-50 border-b border-border bg-card/80 backdrop-blur-md">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-primary to-primary-glow"></div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-primary to-primary-glow bg-clip-text text-transparent">
              Community Hub
            </h1>
          </div>
          
          <div className="flex items-center space-x-1">
            {navItems.map(({ path, label, icon: Icon }) => (
              <Button
                key={path}
                variant={location.pathname === path ? "default" : "ghost"}
                size="sm"
                asChild
                className="transition-all duration-300"
              >
                <Link to={path} className="flex items-center space-x-2">
                  <Icon size={16} />
                  <span className="hidden sm:inline">{label}</span>
                </Link>
              </Button>
            ))}
            
            <div className="ml-4 pl-4 border-l border-border">
              {isAuthenticated ? (
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-2 px-3 py-1 bg-muted rounded-md">
                    <User size={14} />
                    <span className="text-sm font-medium">{profile?.name}</span>
                    <span className="text-xs text-muted-foreground">({profile?.role})</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={logout}
                    className="text-destructive hover:text-destructive"
                  >
                    <LogOut size={16} />
                    <span className="hidden sm:inline ml-2">Logout</span>
                  </Button>
                </div>
              ) : (
                <Button variant="ghost" size="sm" asChild>
                  <Link to="/login" className="flex items-center space-x-2">
                    <LogIn size={16} />
                    <span className="hidden sm:inline">Login</span>
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;